"""
Rank Service
Handles leaderboard queries for referrals and withdrawals
"""

import logging
from typing import Dict, List, Any
from config.database import get_collection, COLLECTIONS

logger = logging.getLogger(__name__)

class RankService:
    """Service for rank and leaderboard operations"""
    
    def __init__(self):
        pass
    
    async def get_referral_leaderboard(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top users by referral count"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            
            pipeline = [
                {
                    "$addFields": {
                        "referral_count": {
                            "$size": {
                                "$ifNull": ["$promotion_report", []]
                            }
                        }
                    }
                },
                {
                    "$match": {
                        "referral_count": {"$gt": 0},
                        "banned": {"$ne": True}  # Exclude banned users
                    }
                },
                {
                    "$sort": {
                        "referral_count": -1,
                        "user_id": 1  # Secondary sort for consistency
                    }
                },
                {
                    "$limit": limit
                },
                {
                    "$project": {
                        "user_id": 1,
                        "first_name": 1,
                        "username": 1,
                        "referral_count": 1,
                        "banned": 1
                    }
                }
            ]
            
            cursor = users_collection.aggregate(pipeline)
            leaderboard = await cursor.to_list(length=None)
            
            logger.info(f"Retrieved referral leaderboard with {len(leaderboard)} users")
            return leaderboard
            
        except Exception as e:
            logger.error(f"Error getting referral leaderboard: {e}")
            return []
    
    async def get_withdrawal_leaderboard(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top users by successful withdrawal amount"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            
            pipeline = [
                {
                    "$match": {
                        "successful_withdraw": {"$gt": 0},
                        "banned": {"$ne": True}  # Exclude banned users
                    }
                },
                {
                    "$sort": {
                        "successful_withdraw": -1,
                        "user_id": 1  # Secondary sort for consistency
                    }
                },
                {
                    "$limit": limit
                },
                {
                    "$project": {
                        "user_id": 1,
                        "first_name": 1,
                        "username": 1,
                        "successful_withdraw": 1,
                        "banned": 1
                    }
                }
            ]
            
            cursor = users_collection.aggregate(pipeline)
            leaderboard = await cursor.to_list(length=None)
            
            logger.info(f"Retrieved withdrawal leaderboard with {len(leaderboard)} users")
            return leaderboard
            
        except Exception as e:
            logger.error(f"Error getting withdrawal leaderboard: {e}")
            return []
    
    def format_username(self, user: Dict[str, Any]) -> str:
        """Format username for display"""
        username = user.get('username', '')
        first_name = user.get('first_name', '')
        user_id = user.get('user_id', 0)
        
        if username:
            return f"@{username}"
        elif first_name:
            return first_name
        else:
            return f"User {user_id}"
    
    def get_rank_emoji(self, position: int) -> str:
        """Get emoji for rank position"""
        if position == 1:
            return "🥇"
        elif position == 2:
            return "🥈"
        elif position == 3:
            return "🥉"
        else:
            return "🎖"
    
    def format_currency(self, amount: int) -> str:
        """Format currency amount with proper formatting"""
        return f"₹{amount:,}"
    
    async def get_leaderboard_stats(self) -> Dict[str, Any]:
        """Get general leaderboard statistics"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Count users with referrals
            users_with_referrals = await users_collection.count_documents({
                "promotion_report": {"$exists": True, "$ne": []},
                "banned": {"$ne": True}
            })
            
            # Count users with withdrawals
            users_with_withdrawals = await users_collection.count_documents({
                "successful_withdraw": {"$gt": 0},
                "banned": {"$ne": True}
            })
            
            # Get total referrals across all users
            referral_pipeline = [
                {"$match": {"banned": {"$ne": True}}},
                {"$addFields": {
                    "referral_count": {"$size": {"$ifNull": ["$promotion_report", []]}}
                }},
                {"$group": {
                    "_id": None,
                    "total_referrals": {"$sum": "$referral_count"}
                }}
            ]
            
            referral_result = await users_collection.aggregate(referral_pipeline).to_list(length=1)
            total_referrals = referral_result[0]['total_referrals'] if referral_result else 0
            
            # Get total withdrawals across all users
            withdrawal_pipeline = [
                {"$match": {"banned": {"$ne": True}}},
                {"$group": {
                    "_id": None,
                    "total_withdrawals": {"$sum": "$successful_withdraw"}
                }}
            ]
            
            withdrawal_result = await users_collection.aggregate(withdrawal_pipeline).to_list(length=1)
            total_withdrawals = withdrawal_result[0]['total_withdrawals'] if withdrawal_result else 0
            
            return {
                "users_with_referrals": users_with_referrals,
                "users_with_withdrawals": users_with_withdrawals,
                "total_referrals": total_referrals,
                "total_withdrawals": total_withdrawals
            }
            
        except Exception as e:
            logger.error(f"Error getting leaderboard stats: {e}")
            return {
                "users_with_referrals": 0,
                "users_with_withdrawals": 0,
                "total_referrals": 0,
                "total_withdrawals": 0
            }
